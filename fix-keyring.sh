#!/bin/bash

# Fix GNOME Keyring for Git Credential Manager
# This script starts the keyring daemon and configures it properly

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Fixing GNOME Keyring for Git Credential Manager..."

# Start the GNOME Keyring daemon
print_status "Starting GNOME Keyring daemon..."

# Kill any existing keyring processes
pkill -f gnome-keyring-daemon || true

# Start the keyring daemon with the secrets component
eval $(gnome-keyring-daemon --start --components=secrets)

# Export the environment variables for the current session
export GNOME_KEYRING_CONTROL
export SSH_AUTH_SOCK
export GNOME_KEYRING_PID

print_success "GNOME Keyring daemon started"

# Add environment variables to shell profile for future sessions
print_status "Configuring environment variables for future sessions..."

# Create the configuration for .bashrc
KEYRING_CONFIG='
# GNOME Keyring configuration for Git Credential Manager
if [ -n "$DESKTOP_SESSION" ]; then
    eval $(gnome-keyring-daemon --start --components=secrets 2>/dev/null)
    export GNOME_KEYRING_CONTROL
    export SSH_AUTH_SOCK
    export GNOME_KEYRING_PID
fi'

# Add to .bashrc if not already present
if ! grep -q "GNOME_KEYRING_CONTROL" ~/.bashrc 2>/dev/null; then
    echo "$KEYRING_CONFIG" >> ~/.bashrc
    print_success "Added keyring configuration to ~/.bashrc"
else
    print_status "Keyring configuration already exists in ~/.bashrc"
fi

# Also add to .profile for other shells
if ! grep -q "GNOME_KEYRING_CONTROL" ~/.profile 2>/dev/null; then
    echo "$KEYRING_CONFIG" >> ~/.profile
    print_success "Added keyring configuration to ~/.profile"
else
    print_status "Keyring configuration already exists in ~/.profile"
fi

# Test if the secret service is working
print_status "Testing secret service..."
if echo "test" | secret-tool store --label="test" service "test" username "test" 2>/dev/null; then
    secret-tool clear service "test" username "test" 2>/dev/null
    print_success "Secret service is working correctly"
else
    print_warning "Secret service test failed, but this might be normal on first run"
fi

# Alternative: Configure Git Credential Manager to use cache instead of secret service
print_status "Configuring fallback credential storage..."
git config --global credential.credentialStore cache
git config --global credential.cacheOptions "--timeout 7200"  # 2 hours

print_success "Configuration complete!"
print_status "You can now try cloning your repository again:"
echo "git clone https://github.com/yukazakiri/DccpAdminV2.git"
print_warning "If you still have issues, restart your terminal or log out and back in."
