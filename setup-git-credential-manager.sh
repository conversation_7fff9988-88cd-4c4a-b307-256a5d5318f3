#!/bin/bash

# Git Credential Manager Setup Script for Arch Linux
# This script installs and configures Git Credential Manager for browser-based authentication

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if running as root
if [[ $EUID -eq 0 ]]; then
   print_error "This script should not be run as root"
   exit 1
fi

print_status "Starting Git Credential Manager setup for Arch Linux..."

# Update system packages
print_status "Updating system packages..."
sudo pacman -Syu --noconfirm

# Install required dependencies
print_status "Installing required dependencies..."
sudo pacman -S --needed --noconfirm git dotnet-runtime

# Check if yay is installed (AUR helper)
if ! command -v yay &> /dev/null; then
    print_warning "yay AUR helper not found. Installing yay..."
    
    # Install base-devel and git if not already installed
    sudo pacman -S --needed --noconfirm base-devel git
    
    # Clone and install yay
    cd /tmp
    git clone https://aur.archlinux.org/yay.git
    cd yay
    makepkg -si --noconfirm
    cd ~
    
    print_success "yay installed successfully"
fi

# Install Git Credential Manager from AUR
print_status "Installing Git Credential Manager from AUR..."
yay -S --noconfirm git-credential-manager-core

# Configure Git to use Git Credential Manager
print_status "Configuring Git to use Git Credential Manager..."

# Set the credential helper
git config --global credential.helper manager-core

# Configure credential manager settings for better browser experience
git config --global credential.credentialStore secretservice
git config --global credential.guiPrompt false

# Configure for GitHub (most common use case)
git config --global credential.https://github.com.provider github

# Configure for GitLab
git config --global credential.https://gitlab.com.provider gitlab

# Configure for Azure DevOps
git config --global credential.https://dev.azure.com.provider azure-repos

print_success "Git Credential Manager configured successfully!"

# Display current Git configuration
print_status "Current Git credential configuration:"
echo "----------------------------------------"
git config --global --get-regexp credential
echo "----------------------------------------"

# Test the setup
print_status "Testing the setup..."
echo ""
print_warning "To test the setup, try cloning a private repository:"
echo "git clone https://github.com/your-username/your-private-repo.git"
echo ""
print_warning "On first use, Git Credential Manager will:"
echo "1. Open your default web browser"
echo "2. Prompt you to authenticate with your Git provider"
echo "3. Store the credentials securely for future use"
echo ""

# Additional configuration options
print_status "Additional configuration options:"
echo ""
echo "To configure specific providers, you can use:"
echo "  git config --global credential.https://your-git-server.com.provider generic"
echo ""
echo "To use different credential stores:"
echo "  git config --global credential.credentialStore cache        # In-memory cache"
echo "  git config --global credential.credentialStore plaintext    # Plain text (not recommended)"
echo "  git config --global credential.credentialStore secretservice # GNOME Keyring/KDE Wallet"
echo ""

# Check if desktop environment supports secret service
if command -v gnome-keyring-daemon &> /dev/null || command -v kwalletd5 &> /dev/null; then
    print_success "Desktop secret service detected. Credentials will be stored securely."
else
    print_warning "No desktop secret service detected. Consider installing gnome-keyring or kwallet for secure credential storage."
    echo "To install gnome-keyring: sudo pacman -S gnome-keyring"
    echo "To install kwallet: sudo pacman -S kwallet"
fi

print_success "Git Credential Manager setup completed!"
print_status "You can now clone private repositories using HTTPS URLs and authenticate via browser."
