# Git Credential Manager Setup for Arch Linux

This repository contains scripts to automatically set up Git Credential Manager on Arch Linux, enabling browser-based authentication for cloning private repositories.

## What This Does

- Installs Git Credential Manager Core from AUR
- Configures Git to use browser-based authentication
- Sets up secure credential storage using your desktop's keyring
- Configures providers for GitHub, GitLab, and Azure DevOps

## Prerequisites

- Arch Linux system
- Internet connection
- User account with sudo privileges (do NOT run as root)

## Quick Start

1. **Run the setup script:**
   ```bash
   ./setup-git-credential-manager.sh
   ```

2. **Test by cloning a private repository:**
   ```bash
   git clone https://github.com/your-username/your-private-repo.git
   ```

3. **On first use:**
   - Your browser will open automatically
   - Authenticate with your Git provider (GitHub, GitLab, etc.)
   - Credentials will be stored securely for future use

## Scripts Included

### `setup-git-credential-manager.sh`
Main setup script that:
- Updates system packages
- Installs yay (AUR helper) if not present
- Installs Git Credential Manager Core
- Configures Git for browser authentication
- Sets up secure credential storage

### `troubleshoot-git-credentials.sh`
Diagnostic script that:
- Checks installation status
- Verifies Git configuration
- Tests connectivity to Git providers
- Provides troubleshooting suggestions

## How It Works

1. **Installation**: Installs Git Credential Manager Core from AUR
2. **Configuration**: Sets up Git to use the credential manager
3. **Authentication**: On first repository access, opens browser for OAuth
4. **Storage**: Stores tokens securely in your desktop keyring
5. **Reuse**: Automatically uses stored credentials for future operations

## Supported Git Providers

- GitHub
- GitLab
- Azure DevOps
- Any Git provider supporting OAuth/browser authentication

## Credential Storage Options

The script configures secure storage by default:
- **secretservice**: Uses GNOME Keyring or KDE Wallet (recommended)
- **cache**: In-memory storage (temporary)
- **plaintext**: Plain text storage (not recommended)

## Troubleshooting

If you encounter issues:

1. **Run the troubleshooting script:**
   ```bash
   ./troubleshoot-git-credentials.sh
   ```

2. **Clear stored credentials:**
   ```bash
   git-credential-manager-core erase
   ```

3. **Reset configuration:**
   ```bash
   git config --global --unset-all credential.helper
   git config --global credential.helper manager-core
   ```

4. **Check if keyring is running:**
   ```bash
   ps aux | grep keyring
   ```

## Manual Configuration

You can manually configure specific providers:

```bash
# For a custom Git server
git config --global credential.https://your-git-server.com.provider generic

# Change credential store
git config --global credential.credentialStore cache
```

## Security Notes

- Credentials are stored securely in your desktop keyring
- Browser authentication uses OAuth tokens, not passwords
- Tokens can be revoked from your Git provider's settings
- The credential manager never stores your actual password

## Requirements

- Arch Linux
- Git
- .NET Runtime (installed automatically)
- Desktop environment with keyring support (GNOME/KDE recommended)

## License

These scripts are provided as-is for educational and convenience purposes.
