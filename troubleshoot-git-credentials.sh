#!/bin/bash

# Git Credential Manager Troubleshooting Script
# This script helps diagnose and fix common issues with Git Credential Manager

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_status "Git Credential Manager Troubleshooting Tool"
echo "=============================================="

# Check if Git Credential Manager is installed
print_status "Checking Git Credential Manager installation..."
if command -v git-credential-manager &> /dev/null; then
    print_success "Git Credential Manager is installed"
    git-credential-manager --version
elif command -v git-credential-manager-core &> /dev/null; then
    print_success "Git Credential Manager is installed (legacy name)"
    git-credential-manager-core --version
else
    print_error "Git Credential Manager is not installed"
    echo "Run the setup script first: ./setup-git-credential-manager.sh"
    exit 1
fi

# Check Git configuration
print_status "Checking Git credential configuration..."
echo ""

if git config --global credential.helper | grep -q "manager"; then
    print_success "Git is configured to use credential manager"
else
    print_warning "Git is not configured to use credential manager"
    echo "Run: git config --global credential.helper manager"
fi

# Display all credential-related configuration
echo ""
print_status "Current credential configuration:"
git config --global --get-regexp credential || echo "No credential configuration found"

# Check credential store
echo ""
print_status "Checking credential store configuration..."
CRED_STORE=$(git config --global credential.credentialStore 2>/dev/null || echo "not set")
echo "Credential store: $CRED_STORE"

if [[ "$CRED_STORE" == "secretservice" ]]; then
    if command -v gnome-keyring-daemon &> /dev/null || command -v kwalletd5 &> /dev/null; then
        print_success "Secret service credential store is properly configured"
    else
        print_warning "Secret service configured but no keyring daemon found"
        echo "Install gnome-keyring or kwallet for secure storage"
    fi
fi

# Check for stored credentials
echo ""
print_status "Checking for stored credentials..."
if command -v secret-tool &> /dev/null; then
    echo "Stored Git credentials:"
    secret-tool search service git 2>/dev/null || echo "No stored credentials found"
else
    print_warning "secret-tool not available. Install libsecret for credential inspection"
fi

# Test network connectivity to common Git providers
echo ""
print_status "Testing connectivity to Git providers..."
for provider in "github.com" "gitlab.com" "dev.azure.com"; do
    if curl -s --connect-timeout 5 "https://$provider" > /dev/null; then
        print_success "✓ $provider is reachable"
    else
        print_error "✗ $provider is not reachable"
    fi
done

# Provide troubleshooting suggestions
echo ""
print_status "Common troubleshooting steps:"
echo ""
echo "1. Clear stored credentials:"
echo "   git-credential-manager erase"
echo ""
echo "2. Reset Git credential configuration:"
echo "   git config --global --unset-all credential.helper"
echo "   git config --global credential.helper manager"
echo ""
echo "3. Test with a simple clone:"
echo "   git clone https://github.com/your-username/your-private-repo.git"
echo ""
echo "4. Check Git Credential Manager logs:"
echo "   journalctl --user -u git-credential-manager"
echo ""
echo "5. Manually trigger authentication:"
echo "   echo 'protocol=https' | git-credential-manager get"
echo "   host=github.com"
echo "   (Press Enter twice)"

print_status "Troubleshooting complete!"
